#!/usr/bin/env node

/**
 * Manufacturing ERP - Render Database Migration Script
 * 
 * This script applies the complete database schema to the Render PostgreSQL instance
 * Optimized for Singapore region deployment for Chinese users
 */

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function runMigration() {
  log('🚀 Manufacturing ERP - Render Database Migration', 'cyan');
  log('================================================', 'cyan');

  // Check for required environment variables
  const databaseUrl = process.env.DATABASE_URL;
  if (!databaseUrl) {
    log('❌ ERROR: DATABASE_URL environment variable is required', 'red');
    log('This should be automatically set by Render PostgreSQL service', 'yellow');
    process.exit(1);
  }

  log(`📍 Region: Singapore (Optimized for Chinese users)`, 'blue');
  log(`🔗 Connecting to database...`, 'blue');

  const client = new Client({
    connectionString: databaseUrl,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
  });

  try {
    // Connect to database
    await client.connect();
    log('✅ Database connection established', 'green');

    // Read the migration SQL file
    const sqlFilePath = path.join(__dirname, 'render-db-setup.sql');
    if (!fs.existsSync(sqlFilePath)) {
      throw new Error(`Migration file not found: ${sqlFilePath}`);
    }

    const migrationSQL = fs.readFileSync(sqlFilePath, 'utf8');
    log('📄 Migration SQL loaded', 'blue');

    // Execute migration
    log('🔄 Executing database migration...', 'yellow');
    await client.query(migrationSQL);
    log('✅ Database migration completed successfully', 'green');

    // Verify migration
    log('🔍 Verifying migration...', 'blue');
    
    // Check if core tables exist
    const tableCheck = await client.query(`
      SELECT tablename 
      FROM pg_tables 
      WHERE schemaname = 'public' 
        AND tablename IN ('companies', 'customers', 'suppliers', 'products')
      ORDER BY tablename
    `);

    log(`📊 Tables created: ${tableCheck.rows.length}`, 'green');
    tableCheck.rows.forEach(row => {
      log(`   ✓ ${row.tablename}`, 'green');
    });

    // Check indexes
    const indexCheck = await client.query(`
      SELECT COUNT(*) as index_count
      FROM pg_indexes 
      WHERE schemaname = 'public' 
        AND tablename IN ('companies', 'customers', 'suppliers', 'products')
    `);

    log(`📈 Indexes created: ${indexCheck.rows[0].index_count}`, 'green');

    // Show database info
    const dbInfo = await client.query(`
      SELECT 
        current_database() as database_name,
        current_user as user_name,
        version() as postgres_version
    `);

    log('📋 Database Information:', 'cyan');
    log(`   Database: ${dbInfo.rows[0].database_name}`, 'blue');
    log(`   User: ${dbInfo.rows[0].user_name}`, 'blue');
    log(`   Version: ${dbInfo.rows[0].postgres_version.split(' ')[0]} ${dbInfo.rows[0].postgres_version.split(' ')[1]}`, 'blue');

    log('', '');
    log('🎉 Migration completed successfully!', 'green');
    log('🌏 Database is ready for Chinese users (Singapore region)', 'green');
    log('', '');
    log('Next steps:', 'cyan');
    log('1. Deploy your web service to Render', 'blue');
    log('2. Set environment variables in Render dashboard', 'blue');
    log('3. Test the application', 'blue');

  } catch (error) {
    log('❌ Migration failed:', 'red');
    log(error.message, 'red');
    
    if (error.code) {
      log(`Error Code: ${error.code}`, 'yellow');
    }
    
    process.exit(1);
  } finally {
    await client.end();
    log('🔌 Database connection closed', 'blue');
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  log('❌ Unhandled Rejection at:', 'red');
  log(promise, 'red');
  log('Reason:', 'red');
  log(reason, 'red');
  process.exit(1);
});

// Run migration
if (require.main === module) {
  runMigration().catch(error => {
    log('❌ Fatal error:', 'red');
    log(error.message, 'red');
    process.exit(1);
  });
}

module.exports = { runMigration };
