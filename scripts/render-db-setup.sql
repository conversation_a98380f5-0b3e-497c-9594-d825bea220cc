-- ============================================================================
-- Manufacturing ERP - Render PostgreSQL Database Setup
-- ============================================================================
-- This script sets up the complete database schema for the Manufacturing ERP
-- system on Render PostgreSQL (Singapore region)
-- 
-- IMPORTANT: This script should be run on a fresh Render PostgreSQL instance
-- ============================================================================

-- Enable required PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- ============================================================================
-- CORE TABLES SETUP
-- ============================================================================

-- Companies table (Multi-tenant root)
CREATE TABLE IF NOT EXISTS companies (
    id TEXT PRIMARY KEY,
    auth0_user_id TEXT NOT NULL UNIQUE,
    name TEXT NOT NULL,
    legal_name TEXT,
    registration_number TEXT,
    tax_id TEXT,
    vat_number TEXT,
    display_name TEXT,
    email TEXT,
    phone TEXT,
    website TEXT,
    address TEXT,
    city TEXT,
    state TEXT,
    postal_code TEXT,
    country TEXT DEFAULT 'China',
    industry TEXT,
    company_size TEXT,
    annual_revenue TEXT,
    currency TEXT DEFAULT 'CNY',
    timezone TEXT DEFAULT 'Asia/Shanghai',
    language TEXT DEFAULT 'zh',
    logo_url TEXT,
    description TEXT,
    founded_year INTEGER,
    business_type TEXT,
    export_license TEXT,
    quality_certifications TEXT[],
    bank_name TEXT,
    bank_account TEXT,
    bank_swift TEXT,
    payment_terms TEXT,
    credit_limit TEXT,
    onboarding_completed BOOLEAN DEFAULT false,
    status TEXT DEFAULT 'active',
    subscription_plan TEXT DEFAULT 'free',
    subscription_status TEXT DEFAULT 'active',
    trial_ends_at TIMESTAMP WITH TIME ZONE,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for companies
CREATE INDEX IF NOT EXISTS companies_auth0_user_id_idx ON companies(auth0_user_id);
CREATE INDEX IF NOT EXISTS companies_status_idx ON companies(status);
CREATE INDEX IF NOT EXISTS companies_created_at_idx ON companies(created_at);

-- Customers table
CREATE TABLE IF NOT EXISTS customers (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id),
    name TEXT NOT NULL,
    contact_name TEXT,
    contact_phone TEXT,
    contact_email TEXT,
    address TEXT,
    tax_id TEXT,
    bank TEXT,
    incoterm TEXT,
    payment_term TEXT,
    status TEXT DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS customers_company_id_idx ON customers(company_id);
CREATE INDEX IF NOT EXISTS customers_status_idx ON customers(status);

-- Suppliers table
CREATE TABLE IF NOT EXISTS suppliers (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id),
    name TEXT NOT NULL,
    contact_name TEXT,
    contact_phone TEXT,
    contact_email TEXT,
    address TEXT,
    tax_id TEXT,
    bank TEXT,
    status TEXT DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS suppliers_company_id_idx ON suppliers(company_id);
CREATE INDEX IF NOT EXISTS suppliers_status_idx ON suppliers(status);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id TEXT PRIMARY KEY,
    company_id TEXT NOT NULL REFERENCES companies(id),
    name TEXT NOT NULL,
    description TEXT,
    category TEXT,
    unit TEXT DEFAULT 'pcs',
    base_price TEXT,
    cost_price TEXT,
    currency TEXT DEFAULT 'CNY',
    hs_code TEXT,
    weight TEXT,
    dimensions TEXT,
    material TEXT,
    color TEXT,
    size TEXT,
    grade TEXT,
    origin_country TEXT DEFAULT 'China',
    min_order_qty TEXT,
    lead_time_days INTEGER,
    shelf_life_days INTEGER,
    storage_conditions TEXT,
    quality_standards TEXT[],
    certifications TEXT[],
    images TEXT[],
    specifications JSONB,
    status TEXT DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS products_company_id_idx ON products(company_id);
CREATE INDEX IF NOT EXISTS products_category_idx ON products(category);
CREATE INDEX IF NOT EXISTS products_status_idx ON products(status);
CREATE INDEX IF NOT EXISTS products_name_search_idx ON products USING gin(name gin_trgm_ops);

-- ============================================================================
-- PERFORMANCE OPTIMIZATIONS
-- ============================================================================

-- Update table statistics
ANALYZE companies;
ANALYZE customers;
ANALYZE suppliers;
ANALYZE products;

-- ============================================================================
-- RENDER-SPECIFIC CONFIGURATIONS
-- ============================================================================

-- Set timezone for Singapore deployment
SET timezone = 'Asia/Singapore';

-- Configure connection pooling parameters
ALTER SYSTEM SET max_connections = 100;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET work_mem = '4MB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';

-- Enable query logging for performance monitoring
ALTER SYSTEM SET log_statement = 'mod';
ALTER SYSTEM SET log_duration = on;
ALTER SYSTEM SET log_min_duration_statement = 1000;

-- Reload configuration
SELECT pg_reload_conf();

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Verify tables were created
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
    AND tablename IN ('companies', 'customers', 'suppliers', 'products')
ORDER BY tablename;

-- Verify indexes were created
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
    AND tablename IN ('companies', 'customers', 'suppliers', 'products')
ORDER BY tablename, indexname;

-- Show database configuration
SELECT name, setting, unit, context 
FROM pg_settings 
WHERE name IN ('max_connections', 'shared_buffers', 'effective_cache_size', 'work_mem')
ORDER BY name;

-- ============================================================================
-- SETUP COMPLETE
-- ============================================================================

SELECT 'Manufacturing ERP Database Setup Complete!' as status,
       'Singapore Region - Optimized for Chinese Users' as region,
       NOW() as completed_at;
