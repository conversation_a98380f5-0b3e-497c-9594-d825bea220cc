#!/bin/bash

#==============================================================================
# Manufacturing ERP - Deployment Platform Switch Script
#==============================================================================
# This script helps switch between Vercel and Render deployment configurations
# while maintaining zero breaking changes to local development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_usage() {
    echo "Manufacturing ERP - Deployment Platform Switch"
    echo "=============================================="
    echo ""
    echo "Usage: $0 [platform]"
    echo ""
    echo "Platforms:"
    echo "  vercel    - Configure for Vercel deployment"
    echo "  render    - Configure for Render deployment (default)"
    echo "  status    - Show current deployment configuration"
    echo "  help      - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 render    # Switch to Render configuration"
    echo "  $0 vercel    # Switch to Vercel configuration"
    echo "  $0 status    # Show current status"
}

show_status() {
    echo "Current Deployment Configuration Status:"
    echo "======================================="
    
    if [ -f "vercel.json" ]; then
        echo "✅ Vercel configuration: ACTIVE"
    else
        echo "❌ Vercel configuration: INACTIVE"
    fi
    
    if [ -f "render.yaml" ]; then
        echo "✅ Render configuration: ACTIVE"
    else
        echo "❌ Render configuration: INACTIVE"
    fi
    
    if [ -f ".renderignore" ]; then
        echo "✅ Render ignore file: PRESENT"
    else
        echo "❌ Render ignore file: MISSING"
    fi
    
    if [ -d ".vercel-backup" ]; then
        echo "✅ Vercel backup: AVAILABLE"
    else
        echo "❌ Vercel backup: NOT AVAILABLE"
    fi
    
    echo ""
    echo "Local Development:"
    echo "✅ Always available (unaffected by deployment platform)"
}

switch_to_vercel() {
    print_status "Switching to Vercel deployment configuration..."
    
    # Restore Vercel configuration if backed up
    if [ -d ".vercel-backup" ]; then
        if [ -f ".vercel-backup/vercel.json" ]; then
            cp .vercel-backup/vercel.json vercel.json
            print_success "Restored vercel.json"
        fi
        
        if [ -d ".vercel-backup/.vercel" ]; then
            cp -r .vercel-backup/.vercel .vercel
            print_success "Restored .vercel directory"
        fi
    else
        print_warning "No Vercel backup found. You may need to reconfigure Vercel manually."
    fi
    
    # Keep Render files but make them inactive
    if [ -f "render.yaml" ]; then
        mv render.yaml render.yaml.inactive
        print_status "Render configuration made inactive (render.yaml.inactive)"
    fi
    
    print_success "Switched to Vercel deployment configuration"
    print_warning "Remember to update Auth0 callback URLs for Vercel domain"
}

switch_to_render() {
    print_status "Switching to Render deployment configuration..."
    
    # Backup Vercel configuration
    mkdir -p .vercel-backup
    
    if [ -f "vercel.json" ]; then
        cp vercel.json .vercel-backup/
        rm vercel.json
        print_status "Vercel configuration backed up and removed"
    fi
    
    if [ -d ".vercel" ]; then
        cp -r .vercel .vercel-backup/
        rm -rf .vercel
        print_status "Vercel directory backed up and removed"
    fi
    
    # Activate Render configuration
    if [ -f "render.yaml.inactive" ]; then
        mv render.yaml.inactive render.yaml
        print_status "Render configuration activated"
    elif [ ! -f "render.yaml" ]; then
        print_error "render.yaml not found. Please ensure Render configuration exists."
        exit 1
    fi
    
    # Ensure .renderignore exists
    if [ ! -f ".renderignore" ]; then
        print_warning ".renderignore not found. Creating basic version..."
        echo "vercel.json" > .renderignore
        echo ".vercel/" >> .renderignore
        echo ".vercel-backup/" >> .renderignore
    fi
    
    print_success "Switched to Render deployment configuration"
    print_warning "Remember to update Auth0 callback URLs for Render domain"
}

# Main script logic
case "${1:-render}" in
    "vercel")
        switch_to_vercel
        ;;
    "render")
        switch_to_render
        ;;
    "status")
        show_status
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        print_error "Unknown platform: $1"
        echo ""
        show_usage
        exit 1
        ;;
esac

echo ""
print_status "Local development remains unaffected by deployment platform changes"
print_status "Use 'npm run dev' to continue local development as usual"
