#!/bin/bash

#==============================================================================
# Manufacturing ERP - Render Build Optimization Script
#==============================================================================
# This script optimizes the build process for Render deployment
# Includes dependency cleanup, build optimization, and performance tuning

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚀 Manufacturing ERP - Render Build Optimization"
echo "================================================"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

# 1. Clean previous builds
print_status "Cleaning previous builds..."
rm -rf .next
rm -rf out
rm -rf dist
rm -rf node_modules/.cache
print_success "Build artifacts cleaned"

# 2. Use Render-optimized Next.js config
print_status "Switching to Render-optimized Next.js configuration..."
if [ -f "next.config.render.mjs" ]; then
    cp next.config.mjs next.config.local.mjs.backup 2>/dev/null || true
    cp next.config.render.mjs next.config.mjs
    print_success "Render-optimized Next.js config activated"
else
    print_warning "next.config.render.mjs not found, using default configuration"
fi

# 3. Install dependencies with production optimizations
print_status "Installing dependencies with production optimizations..."
export NODE_ENV=production
npm ci --only=production --no-audit --no-fund
print_success "Production dependencies installed"

# 4. Install dev dependencies needed for build
print_status "Installing build dependencies..."
npm install --only=dev --no-audit --no-fund
print_success "Build dependencies installed"

# 5. Generate database types (if needed)
print_status "Generating database types..."
if npm run db:generate > /dev/null 2>&1; then
    print_success "Database types generated"
else
    print_warning "Database type generation skipped (may not be needed)"
fi

# 6. Run production build with optimizations
print_status "Running optimized production build..."
export NODE_ENV=production
export NEXT_TELEMETRY_DISABLED=1
export DEPLOYMENT_PLATFORM=render
export DEPLOYMENT_REGION=singapore

if npm run build; then
    print_success "Production build completed successfully"
else
    print_error "Production build failed"
    
    # Restore original config if build failed
    if [ -f "next.config.local.mjs.backup" ]; then
        cp next.config.local.mjs.backup next.config.mjs
        print_status "Restored original Next.js configuration"
    fi
    
    exit 1
fi

# 7. Analyze build output
print_status "Analyzing build output..."
BUILD_SIZE=$(du -sh .next 2>/dev/null | cut -f1 || echo "Unknown")
print_success "Build size: $BUILD_SIZE"

# Check for large bundles
if [ -d ".next/static/chunks" ]; then
    LARGE_CHUNKS=$(find .next/static/chunks -name "*.js" -size +500k 2>/dev/null | wc -l)
    if [ "$LARGE_CHUNKS" -gt 0 ]; then
        print_warning "Found $LARGE_CHUNKS large chunks (>500KB). Consider code splitting."
    else
        print_success "No large chunks detected"
    fi
fi

# 8. Optimize for Render deployment
print_status "Applying Render-specific optimizations..."

# Create .render directory for deployment metadata
mkdir -p .render
echo "singapore" > .render/region
echo "$(date -u +%Y-%m-%dT%H:%M:%SZ)" > .render/build-timestamp
echo "production" > .render/environment

print_success "Render deployment metadata created"

# 9. Verify critical files exist
print_status "Verifying critical files..."
CRITICAL_FILES=(".next/BUILD_ID" ".next/static" "package.json")
for file in "${CRITICAL_FILES[@]}"; do
    if [ -e "$file" ]; then
        print_success "✓ $file exists"
    else
        print_error "✗ $file missing"
        exit 1
    fi
done

# 10. Performance recommendations
echo ""
print_status "Performance Recommendations:"
echo "• Database: Use connection pooling (configured)"
echo "• Caching: Enable Redis for session storage (optional)"
echo "• CDN: Render provides global CDN automatically"
echo "• Monitoring: Consider adding performance monitoring"
echo "• Scaling: Monitor response times and scale as needed"

echo ""
print_success "🎉 Render build optimization completed!"
print_status "Your application is optimized for deployment to Render (Singapore region)"
print_status "Next step: Deploy to Render using the web dashboard or CLI"

# Restore original config for local development
if [ -f "next.config.local.mjs.backup" ]; then
    print_status "Keeping Render config for deployment, backup available for local dev"
else
    print_status "Render configuration active"
fi

echo ""
print_status "Build optimization summary:"
echo "✅ Dependencies optimized for production"
echo "✅ Next.js configuration optimized for Render"
echo "✅ Build artifacts generated and verified"
echo "✅ Render deployment metadata created"
echo "✅ Performance optimizations applied"
echo "🌏 Optimized for Chinese users (Singapore region)"
