#!/bin/bash

#==============================================================================
# Manufacturing ERP - Render Deployment Script
#==============================================================================
# This script helps prepare and deploy the Manufacturing ERP system to Render
# Optimized for Chinese users with Singapore region selection

set -e  # Exit on any error

echo "🚀 Manufacturing ERP - Render Deployment Preparation"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

print_status "Checking project structure..."

# Verify essential files exist
REQUIRED_FILES=("package.json" "next.config.mjs" "lib/db.ts" "lib/schema-postgres.ts")
for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        print_error "Required file missing: $file"
        exit 1
    fi
done

print_success "Project structure verified"

# Check Node.js version
NODE_VERSION=$(node --version)
print_status "Node.js version: $NODE_VERSION"

# Install dependencies
print_status "Installing dependencies..."
npm ci

# Run type checking (ignore errors for now, just warn)
print_status "Running type check..."
if npm run type-check; then
    print_success "Type check passed"
else
    print_warning "Type check has warnings/errors - deployment may still work"
fi

# Test build process
print_status "Testing production build..."
if npm run build; then
    print_success "Production build successful"
else
    print_error "Production build failed. Please fix build errors before deploying."
    exit 1
fi

# Clean up build artifacts
print_status "Cleaning up build artifacts..."
rm -rf .next

print_success "Build test completed successfully"

echo ""
echo "🎯 Render Deployment Checklist:"
echo "================================"
echo "✅ 1. Project structure verified"
echo "✅ 2. Dependencies installed"
echo "✅ 3. Production build tested"
echo ""
echo "📋 Next Steps for Render Deployment:"
echo "1. Create PostgreSQL database on Render (Singapore region)"
echo "2. Create web service on Render (Singapore region)"
echo "3. Set environment variables in Render dashboard"
echo "4. Connect GitHub repository to Render"
echo "5. Deploy and test"
echo ""
echo "🌏 Region Configuration:"
echo "- Database: Singapore (closest to China)"
echo "- Web Service: Singapore (optimal for Chinese users)"
echo ""
echo "📁 Configuration Files Created:"
echo "- render.yaml (Infrastructure as Code)"
echo "- .env.render (Environment template)"
echo ""
print_success "Render deployment preparation completed!"
