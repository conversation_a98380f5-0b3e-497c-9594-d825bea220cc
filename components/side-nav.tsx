"use client"

import type React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  LayoutDashboard,
  Boxes,
  Handshake,
  Factory,
  Package,
  Package2,
  Ship,
  Coins,
  BookText,
  CheckCircle,
  Truck,
  BarChart3,
  FileText,
  Palette,
  MapPin,
  TestTube,
  Shield,
  Users,
  Building,
  ShoppingCart,
  ShoppingBag,
  Settings,
  TrendingUp,
  Layers,
} from "lucide-react"
import { useI18n } from "./i18n-provider"

type NavItem = {
  href: string
  key: string
  icon: React.ComponentType<{ className?: string }>
}

type NavGroup = {
  titleKey: string
  items: NavItem[]
}

export function SideNav({ onNavigate }: { onNavigate?: () => void }) {
  const pathname = usePathname()
  const { t } = useI18n()

  // ✅ OPTIMIZED NAVIGATION STRUCTURE
  // Following textile export manufacturing workflow: Samples → Contracts → Planning → Production → Quality → Inventory → Shipping → Export
  const navGroups: NavGroup[] = [
    // 1. OVERVIEW (Fixed dashboard route)
    {
      titleKey: "nav.group.overview",
      items: [{ href: "/dashboard", key: "nav.item.dashboard", icon: LayoutDashboard }],
    },

    // 2. MASTER DATA (Streamlined - removed samples, moved to sales process)
    {
      titleKey: "nav.group.master-data",
      items: [
        { href: "/customers", key: "nav.item.customers", icon: Users },
        { href: "/suppliers", key: "nav.item.suppliers", icon: Building },
        { href: "/products", key: "nav.item.products", icon: Package },
      ],
    },

    // 3. SALES PROCESS (Enhanced with samples at the beginning)
    {
      titleKey: "nav.group.sales-process",
      items: [
        { href: "/samples", key: "nav.item.samples", icon: TestTube }, // ✅ MOVED: Samples logically precede contracts
        { href: "/sales-contracts", key: "nav.item.sales-contracts", icon: ShoppingCart },
        { href: "/purchase-contracts", key: "nav.item.purchase-contracts", icon: ShoppingBag },
      ],
    },

    // 4. PRODUCTION PLANNING (Reordered for workflow logic)
    {
      titleKey: "nav.group.production-planning",
      items: [
        { href: "/planning", key: "nav.item.mrp-planning", icon: TrendingUp }, // ✅ MOVED: Planning precedes execution
        { href: "/bom", key: "nav.item.bom-management", icon: Layers },
        { href: "/production", key: "nav.item.work-orders", icon: Factory },
      ],
    },

    // 5. QUALITY & INVENTORY (Combined for workflow efficiency)
    {
      titleKey: "nav.group.quality-inventory",
      items: [
        { href: "/quality", key: "nav.item.quality-control", icon: CheckCircle }, // ✅ MOVED: Quality decisions affect inventory
        { href: "/inventory", key: "nav.item.inventory", icon: Boxes },
        { href: "/raw-materials", key: "nav.item.raw-materials", icon: Package2 },
        { href: "/locations", key: "nav.item.locations", icon: MapPin },
      ],
    },

    // 6. SHIPPING & EXPORT (Combined for export workflow)
    {
      titleKey: "nav.group.shipping-export",
      items: [
        { href: "/shipping", key: "nav.item.shipping", icon: Truck }, // ✅ MOVED: Shipping preparation
        { href: "/export", key: "nav.item.export-declarations", icon: Ship }, // ✅ MOVED: Export follows shipping
      ],
    },

    // 7. FINANCE & REPORTING (Unchanged)
    {
      titleKey: "nav.group.finance-reporting",
      items: [
        { href: "/finance", key: "nav.item.accounting", icon: Coins },
        { href: "/reports", key: "nav.item.reports", icon: BarChart3 },
      ],
    },

    // 8. ADMINISTRATION (Enhanced with templates)
    {
      titleKey: "nav.group.administration",
      items: [
        { href: "/contract-templates", key: "nav.item.contract-templates", icon: BookText }, // ✅ MOVED: Administrative function
        { href: "/company-profile", key: "nav.item.company-profile", icon: Settings },
      ],
    },
  ]

  return (
    <aside className="h-full w-full bg-sidebar">
      <div className="h-full bg-sidebar px-3 py-4">
        {navGroups.map((group) => (
          <div key={group.titleKey} className="mb-6">
            <div className="px-3 mb-3 text-xs font-semibold uppercase tracking-wider text-sidebar-foreground/70 bg-sidebar">
              {t(group.titleKey)}
            </div>
            <nav className="space-y-1 bg-sidebar">
              {group.items.map((item) => {
                const active = pathname === item.href
                const Icon = item.icon
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    onClick={onNavigate}
                    className={cn(
                      "flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200",
                      active
                        ? "bg-sidebar-primary text-sidebar-primary-foreground shadow-sm"
                        : "text-sidebar-foreground/80 hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                    )}
                    aria-current={active ? "page" : undefined}
                  >
                    <Icon className="h-4 w-4 flex-shrink-0" />
                    <span className="flex-1 truncate">{t(item.key)}</span>
                  </Link>
                )
              })}
            </nav>
          </div>
        ))}
      </div>
    </aside>
  )
}
