# Manufacturing ERP - Render Deployment Guide

## 🌏 Optimized for Chinese Users (Singapore Region)

This guide provides step-by-step instructions for deploying the Manufacturing ERP system to Render, optimized for Chinese users with Singapore region deployment.

## 📋 Prerequisites

- [x] Render account created
- [x] GitHub repository with Manufacturing ERP code
- [x] Auth0 account configured
- [x] Local development environment working

## 🚀 Deployment Steps

### Step 1: Database Setup

1. **PostgreSQL Database Created** ✅
   - Database ID: `dpg-d379fsemcj7s73fg7ue0-a`
   - Region: Singapore
   - Plan: Free (can be upgraded)
   - Database Name: `manufacturing_erp_db`
   - User: `manufacturing_erp_db_user`

2. **Run Database Migration**
   ```bash
   # After web service is deployed, run this command in Render shell
   npm run render:db-setup
   ```

### Step 2: Web Service Deployment

1. **Create Web Service on Render**
   - Go to [Render Dashboard](https://dashboard.render.com)
   - Click "New +" → "Web Service"
   - Connect your GitHub repository
   - Configure as follows:

2. **Service Configuration**
   ```yaml
   Name: manufacturing-erp-web
   Region: Singapore
   Branch: main
   Runtime: Node
   Build Command: npm run render:build
   Start Command: npm run render:start
   Plan: Starter (can be upgraded)
   ```

3. **Environment Variables**
   Set these in Render Dashboard → Environment:

   ```bash
   # Database (Auto-set by Render)
   DATABASE_URL=<automatically_provided_by_render>
   
   # Application
   NODE_ENV=production
   USE_POSTGRESQL=true
   MIGRATION_PHASE=production
   DEPLOYMENT_REGION=singapore
   
   # Auth0 Configuration
   AUTH0_SECRET=****************************************************************
   AUTH0_BASE_URL=https://your-app-name.onrender.com
   AUTH0_ISSUER_BASE_URL=https://dev-tejx02ztaj7oufoc.us.auth0.com
   AUTH0_CLIENT_ID=To2oAQX05DtstsgKXdLvsUYAaRO23QCK
   AUTH0_CLIENT_SECRET=****************************************************************
   
   # Application URLs
   NEXT_PUBLIC_APP_URL=https://your-app-name.onrender.com
   
   # Security
   ENFORCE_TENANT_ISOLATION=true
   ENABLE_AUDIT_LOGGING=true
   ENABLE_SSL_REQUIRE=true
   
   # Performance
   LOG_LEVEL=info
   ENABLE_QUERY_LOGGING=false
   ENABLE_PERFORMANCE_MONITORING=true
   SLOW_QUERY_THRESHOLD=1000
   ```

### Step 3: Auth0 Configuration Update

1. **Update Auth0 Application Settings**
   - Go to [Auth0 Dashboard](https://manage.auth0.com)
   - Navigate to Applications → Manufacturing ERP
   - Update the following:

   ```
   Allowed Callback URLs:
   https://your-app-name.onrender.com/api/auth/callback
   
   Allowed Logout URLs:
   https://your-app-name.onrender.com
   
   Allowed Web Origins:
   https://your-app-name.onrender.com
   ```

### Step 4: Deploy and Test

1. **Deploy Application**
   - Render will automatically deploy when you push to main branch
   - Monitor deployment logs in Render Dashboard

2. **Run Database Migration**
   ```bash
   # In Render Shell (after first deployment)
   npm run render:db-setup
   ```

3. **Test Application**
   - Visit your Render app URL
   - Test login/logout functionality
   - Verify multi-tenant isolation
   - Test core ERP features

## 🔧 Useful Commands

```bash
# Local preparation
npm run render:prepare          # Prepare for deployment
npm run render:deploy-check     # Check if ready to deploy

# Production commands (run in Render shell)
npm run render:db-setup         # Setup database schema
npm run render:build            # Build application
npm run render:start            # Start application
```

## 🌏 China-Specific Optimizations

1. **Region Selection**: Singapore (closest to China)
2. **CDN**: Render's global CDN for faster content delivery
3. **Database**: PostgreSQL in Singapore region
4. **Timezone**: Asia/Shanghai configured in database
5. **Language**: Chinese (Simplified) support enabled

## 📊 Performance Monitoring

- **Response Times**: Monitor in Render Dashboard
- **Database Performance**: Check PostgreSQL metrics
- **Error Tracking**: Console logs in Render Dashboard
- **Uptime**: Render provides 99.9% uptime SLA

## 🔒 Security Checklist

- [x] Multi-tenant isolation enabled
- [x] SSL/HTTPS enforced
- [x] Auth0 authentication configured
- [x] Environment variables secured
- [x] Database access restricted
- [x] Audit logging enabled

## 🆘 Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Check build logs in Render Dashboard
   # Run locally first: npm run render:deploy-check
   ```

2. **Database Connection Issues**
   ```bash
   # Verify DATABASE_URL is set
   # Check database status in Render Dashboard
   ```

3. **Auth0 Login Issues**
   ```bash
   # Verify callback URLs in Auth0
   # Check AUTH0_BASE_URL matches your Render URL
   ```

## 📞 Support

- **Render Support**: [Render Help Center](https://render.com/docs)
- **Auth0 Support**: [Auth0 Community](https://community.auth0.com)
- **Project Issues**: Create GitHub issue in repository

## 🎯 Next Steps

After successful deployment:

1. **Domain Setup**: Configure custom domain (optional)
2. **Monitoring**: Set up additional monitoring tools
3. **Scaling**: Upgrade plans as needed
4. **Backup**: Configure database backups
5. **CI/CD**: Set up automated deployments

---

**🌟 Your Manufacturing ERP system is now ready for Chinese users with optimal performance from Singapore region!**
