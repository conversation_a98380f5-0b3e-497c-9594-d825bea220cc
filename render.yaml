# Manufacturing ERP - Render Configuration
# This file defines the infrastructure for deploying the Manufacturing ERP system on Render

services:
  # Web Service - Next.js Application
  - type: web
    name: manufacturing-erp-web
    runtime: node
    plan: starter  # Can be upgraded to standard/pro as needed
    region: singapore  # Closest to China for optimal performance
    branch: main
    buildCommand: npm ci && npm run build
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: USE_POSTGRESQL
        value: true
      - key: MIGRATION_PHASE
        value: production
      - key: MIGRATION_DEBUG
        value: false
      - key: ENFORCE_TENANT_ISOLATION
        value: true
      - key: ENABLE_AUDIT_LOGGING
        value: true
      - key: ENABLE_SSL_REQUIRE
        value: true
      - key: LOG_LEVEL
        value: info
      - key: ENABLE_QUERY_LOGGING
        value: false
      - key: ENABLE_PERFORMANCE_MONITORING
        value: true
      - key: SLOW_QUERY_THRESHOLD
        value: 1000
      # Auth0 Configuration (set these in Render dashboard)
      - key: AUTH0_SECRET
        sync: false  # Set manually in Render dashboard
      - key: AUTH0_BASE_URL
        sync: false  # Set to your Render app URL
      - key: AUTH0_ISSUER_BASE_URL
        sync: false  # Set manually in Render dashboard
      - key: AUTH0_CLIENT_ID
        sync: false  # Set manually in Render dashboard
      - key: AUTH0_CLIENT_SECRET
        sync: false  # Set manually in Render dashboard
      - key: NEXT_PUBLIC_APP_URL
        sync: false  # Set to your Render app URL

databases:
  # PostgreSQL Database
  - name: manufacturing-erp-db
    plan: starter  # Can be upgraded as needed
    region: singapore  # Same region as web service, closest to China
    databaseName: manufacturing_erp
    user: erp_user

# Optional: Redis for caching (can be added later)
# - type: redis
#   name: manufacturing-erp-cache
#   plan: starter
#   region: singapore
