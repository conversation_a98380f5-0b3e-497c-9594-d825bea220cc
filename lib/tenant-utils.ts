import { db } from '@/lib/db'
import { companies } from '@/lib/schema-postgres'
import { eq } from 'drizzle-orm'
import { getSession } from '@auth0/nextjs-auth0'
import { cookies } from 'next/headers'

/**
 * Multi-Tenant Utility Functions
 * 
 * These functions provide secure tenant isolation for the Manufacturing ERP system.
 * All ERP data access should go through these utilities to ensure proper multi-tenant security.
 */

export interface TenantContext {
  companyId: string
  userId: string
  company: typeof companies.$inferSelect
}

/**
 * Get the current user's company context from Auth0 session
 * This is the primary function for establishing tenant context in API routes and server components
 *
 * @param request - Optional request object for API routes. If not provided, uses cookies() for server components
 */
export async function getTenantContext(request?: Request): Promise<TenantContext | null> {
  try {
    // ✅ PRODUCTION FIX: Enhanced session handling for Render deployment
    let session

    if (request) {
      // API route context - pass request to getSession
      session = await getSession(request)
    } else {
      // Server component context - handle production environment differences
      try {
        // Try to get session with cookies for server components
        const cookieStore = await cookies()
        session = await getSession()

        // PRODUCTION FIX: If session is null, try alternative approach
        if (!session && process.env.NODE_ENV === 'production') {
          console.log('🔍 Production: Attempting alternative session retrieval')
          // In production, server components might need different session handling
          // This is a fallback for production environment
          session = await getSession()
        }
      } catch (sessionError) {
        console.log('🔍 Session retrieval error:', sessionError)
        return null
      }
    }

    if (!session?.user?.sub) {
      console.log('🔍 No valid session found in getTenantContext', {
        hasSession: !!session,
        hasUser: !!session?.user,
        hasSub: !!session?.user?.sub,
        environment: process.env.NODE_ENV
      })
      return null
    }

    const company = await db.query.companies.findFirst({
      where: eq(companies.auth0_user_id, session.user.sub),
    })

    if (!company) {
      console.log(`🔍 No company found for user: ${session.user.sub}`)
      return null
    }

    console.log(`✅ Tenant context established: Company ${company.id} for user ${session.user.sub}`)
    return {
      companyId: company.id,
      userId: session.user.sub,
      company,
    }
  } catch (error) {
    console.error('❌ Error getting tenant context:', error)
    return null
  }
}

/**
 * Get company by Auth0 user ID
 * Used for associating new data with the correct company
 */
export async function getCompanyByUserId(userId: string) {
  return await db.query.companies.findFirst({
    where: eq(companies.auth0_user_id, userId),
  })
}

/**
 * Validate that a company ID belongs to the current user
 * Used for additional security checks in sensitive operations
 */
export async function validateCompanyAccess(companyId: string, request?: Request): Promise<boolean> {
  try {
    const context = await getTenantContext(request)
    return context?.companyId === companyId
  } catch (error) {
    console.error('Error validating company access:', error)
    return false
  }
}

/**
 * Create a standardized error response for unauthorized access
 */
export function createUnauthorizedResponse() {
  return new Response(
    JSON.stringify({
      error: 'Unauthorized',
      message: 'Access denied. Please ensure you are logged in and have proper permissions.'
    }),
    {
      status: 401,
      headers: { 'Content-Type': 'application/json' }
    }
  )
}

/**
 * Create a standardized error response for forbidden access (valid user, wrong tenant)
 */
export function createForbiddenResponse() {
  return new Response(
    JSON.stringify({
      error: 'Forbidden',
      message: 'Access denied. You do not have permission to access this resource.'
    }),
    {
      status: 403,
      headers: { 'Content-Type': 'application/json' }
    }
  )
}

/**
 * Create a standardized error response for missing company profile
 */
export function createCompanyRequiredResponse() {
  return new Response(
    JSON.stringify({
      error: 'Company Profile Required',
      message: 'Please complete your company profile setup before accessing ERP modules.',
      action: 'redirect_to_onboarding'
    }),
    {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    }
  )
}

/**
 * Middleware wrapper for API routes that require tenant context
 * Usage: export const GET = withTenantAuth(async (request, context) => { ... })
 */
export function withTenantAuth<T extends any[]>(
  handler: (request: Request, context: TenantContext, ...args: T) => Promise<Response>
) {
  return async (request: Request, ...args: T): Promise<Response> => {
    const context = await getTenantContext(request)

    if (!context) {
      return createUnauthorizedResponse()
    }

    // Check if company profile is complete
    if (context.company.onboarding_completed !== 'true') {
      return createCompanyRequiredResponse()
    }

    return handler(request, context, ...args)
  }
}

/**
 * Type-safe query builder that automatically includes company_id filtering
 * This ensures all queries are automatically scoped to the current tenant
 */
export function createTenantQuery(companyId: string) {
  return {
    companyId,
    // Add helper methods for common query patterns
    whereCompany: { company_id: companyId },
  }
}
