# Manufacturing ERP - Render Deployment Ignore File
# Files and directories to exclude from Render deployment

# Vercel-specific files (completely removed)
# vercel.json - removed
# .vercel/ - removed

# Development files
.env.local
.env.development
.env.test

# Testing files
__tests__/
e2e/
test-results/
coverage/
*.test.js
*.test.ts
*.spec.js
*.spec.ts
jest.config.js
jest.setup.js
jest.global-setup.js
jest.global-teardown.js
jest.polyfills.js
playwright.config.ts

# Development tools
.vscode/
.idea/
*.code-workspace
setup-vscode.sh

# Documentation (optional - can be included if needed)
docs/
*.md
README*

# Temporary files
tmp/
temp/
.tmp/
.temp/

# Log files
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next/
out/

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Temporary folders
.tmp/
.temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Local development scripts
scripts/dev-workflow.sh
scripts/check-*.js
scripts/test-*.ts
scripts/test-*.js
scripts/diagnose-*.ts
scripts/migrate-*.ts
scripts/create-test-*.ts
scripts/auth0-*.ts
scripts/complete-security-setup.ts
scripts/comprehensive-security-test.ts

# i18n development files
i18n-backup/
i18n-cicd/
i18n-monitoring/
i18n-parallel/
i18n-production/
i18n-temp/
i18n-ai.config.js
vercel-i18n-check.js

# Database development files
postgres:/
docker/
docker-compose.yml
*.sql.backup

# Backup files
*.backup
*.bak
*~

# Development uploads (use cloud storage in production)
uploads/

# Local configuration files
.env.local
.env.development.local
.env.test.local
