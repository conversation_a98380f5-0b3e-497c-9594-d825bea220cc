/** @type {import('next').NextConfig} */
const nextConfig = {
  // Production optimizations for Render deployment
  eslint: {
    ignoreDuringBuilds: true, // Skip ESLint during build for faster deployment
  },
  typescript: {
    ignoreBuildErrors: true, // Skip TypeScript errors during build (for now)
  },
  
  // Image optimization for Render
  images: {
    unoptimized: true, // Disable Next.js image optimization for Render
    domains: [], // Add your image domains here if needed
  },
  
  // Performance optimizations
  experimental: {
    optimizeCss: true, // Enable CSS optimization
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'], // Optimize package imports
  },
  
  // Compression and caching
  compress: true, // Enable gzip compression
  
  // Output configuration for Render
  output: 'standalone', // Generate standalone output for better performance
  
  // Environment-specific configurations
  env: {
    DEPLOYMENT_PLATFORM: 'render',
    DEPLOYMENT_REGION: 'singapore',
  },
  
  // Headers for security and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
        ],
      },
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, max-age=0',
          },
        ],
      },
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
  
  // Redirects for better SEO
  async redirects() {
    return [
      {
        source: '/home',
        destination: '/',
        permanent: true,
      },
    ];
  },
  
  // Webpack optimizations
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Production optimizations
    if (!dev) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
            },
          },
        },
      };
    }
    
    // Ignore certain modules to reduce bundle size
    config.resolve.alias = {
      ...config.resolve.alias,
      // Add any aliases you need
    };
    
    return config;
  },
  
  // Runtime configuration
  serverRuntimeConfig: {
    // Server-side only configuration
  },
  
  publicRuntimeConfig: {
    // Client-side configuration
    deploymentPlatform: 'render',
    deploymentRegion: 'singapore',
  },
};

export default nextConfig;
