import { jsonError, jsonOk } from "@/lib/api-helpers"
import { db } from "@/lib/db"
import { customers } from "@/lib/schema-postgres"


export async function GET() {
  try {
    let connected = false
    if (process.env.DATABASE_URL) {
      // A simple query to check the connection using schema
      await db.select().from(customers).limit(1)
      connected = true
    }
    return jsonOk({ ok: true, hasDb: !!process.env.DATABASE_URL, connected })
  } catch (e) {
    console.error('Database health check failed:', e)
    return jsonError(e)
  }
}
