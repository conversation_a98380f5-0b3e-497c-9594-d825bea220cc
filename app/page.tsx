"use client"

import { useUser } from '@auth0/nextjs-auth0/client'
import { useRouter, usePathname } from 'next/navigation'
import { useEffect } from 'react'
import LandingPageComponent from './landing/page'

export default function Page() {
  const { user, isLoading } = useUser()
  const router = useRouter()
  const pathname = usePathname()

  // Only redirect authenticated users to dashboard if they're on the root page
  useEffect(() => {
    if (!isLoading && user && pathname === '/') {
      router.push('/dashboard')
    }
  }, [user, isLoading, router, pathname])

  // If loading, show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  // If authenticated and on root page, redirect is handled by useEffect
  if (user && pathname === '/') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Redirecting to dashboard...</p>
        </div>
      </div>
    )
  }

  // If not authenticated, show landing page
  return <LandingPageComponent />
}


